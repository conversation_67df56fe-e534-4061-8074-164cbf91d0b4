import { useAuthStore } from '../utils/authStore';

// TTS WebSocket 请求接口
export interface TTSRequest {
  text: string;
  timbre: string;
  speed_ratio?: number;
  volume_ratio?: number;
  pitch_ratio?: number;
  encoding?: string;
}

// TTS WebSocket 响应接口
export interface TTSResponse {
  success: boolean;
  message: string;
  request_id?: string | null;
}

// TTS 错误响应接口
export interface TTSErrorResponse {
  error_code: number;
  error_message: string;
  request_id?: string | null;
}

// TTS 服务状态
export enum TTSStatus {
  IDLE = 'idle',
  CONNECTING = 'connecting',
  CONNECTED = 'connected',
  PLAYING = 'playing',
  ERROR = 'error',
  COMPLETED = 'completed'
}

// TTS 服务类
export class TTSService {
  private ws: WebSocket | null = null;
  private audioContext: AudioContext | null = null;
  private audioBuffer: AudioBuffer | null = null;
  private audioSource: AudioBufferSourceNode | null = null;
  private status: TTSStatus = TTSStatus.IDLE;
  private audioChunks: ArrayBuffer[] = [];
  private onStatusChange?: (status: TTSStatus) => void;
  private onError?: (error: string) => void;
  private onComplete?: () => void;

  constructor() {
    // 延迟初始化 AudioContext，在用户交互时创建
  }

  private initAudioContext() {
    if (this.audioContext) {
      return; // 已经初始化过了
    }

    try {
      // 创建 AudioContext
      this.audioContext = new (window.AudioContext || (window as any).webkitAudioContext)();
      console.log('AudioContext initialized successfully');
    } catch (error) {
      console.error('Failed to initialize AudioContext:', error);
      throw new Error('无法初始化音频上下文，请检查浏览器支持');
    }
  }

  // 设置状态变化回调
  public setStatusChangeCallback(callback: (status: TTSStatus) => void) {
    this.onStatusChange = callback;
  }

  // 设置错误回调
  public setErrorCallback(callback: (error: string) => void) {
    this.onError = callback;
  }

  // 设置完成回调
  public setCompleteCallback(callback: () => void) {
    this.onComplete = callback;
  }

  // 更新状态
  private updateStatus(status: TTSStatus) {
    this.status = status;
    this.onStatusChange?.(status);
  }

  // 获取当前状态
  public getStatus(): TTSStatus {
    return this.status;
  }

  // 连接 WebSocket
  private async connectWebSocket(): Promise<void> {
    return new Promise((resolve, reject) => {
      try {
        const { user } = useAuthStore.getState();
        if (!user?.token) {
          reject(new Error('未找到有效的认证令牌'));
          return;
        }

        // 构建 WebSocket URL
        const baseUrl = import.meta.env.VITE_API_BASE_URL || 'http://localhost:8000';
        // 移除baseUrl中的/api/v1后缀，因为我们要手动添加完整路径
        const cleanBaseUrl = baseUrl.replace(/\/api\/v1$/, '');
        const wsUrl = cleanBaseUrl.replace(/^http/, 'ws') + `/api/v1/tts/ws?token=${user.token}`;

        this.ws = new WebSocket(wsUrl);

        this.ws.onopen = () => {
          console.log('TTS WebSocket connected');
          this.updateStatus(TTSStatus.CONNECTED);
          resolve();
        };

        this.ws.onmessage = (event) => {
          this.handleWebSocketMessage(event);
        };

        this.ws.onerror = (error) => {
          console.error('TTS WebSocket error:', error);
          this.updateStatus(TTSStatus.ERROR);
          this.onError?.('WebSocket连接错误');
          reject(error);
        };

        this.ws.onclose = (event) => {
          console.log('TTS WebSocket closed:', event.code, event.reason);
          if (event.code === 1008) {
            this.onError?.('认证失败，请检查token是否有效');
          }
          this.cleanup();
        };

      } catch (error) {
        console.error('Failed to create WebSocket:', error);
        reject(error);
      }
    });
  }

  // 处理 WebSocket 消息
  private handleWebSocketMessage(event: MessageEvent) {
    console.log('TTS WebSocket message received:', event.data);

    if (event.data instanceof Blob) {
      // 音频数据
      console.log('Received audio blob, size:', event.data.size);
      event.data.arrayBuffer().then((buffer) => {
        console.log('Audio buffer size:', buffer.byteLength);
        this.audioChunks.push(buffer);
      }).catch((error) => {
        console.error('Failed to convert blob to array buffer:', error);
        this.updateStatus(TTSStatus.ERROR);
        this.onError?.('音频数据处理失败');
      });
    } else if (event.data instanceof ArrayBuffer) {
      // 直接的音频数据
      console.log('Received audio ArrayBuffer, size:', event.data.byteLength);
      this.audioChunks.push(event.data);
    } else {
      // JSON 响应或文本消息
      try {
        const response = JSON.parse(event.data);
        console.log('TTS JSON response:', response);

        if (response.error_code) {
          // 错误响应
          const errorResponse = response as TTSErrorResponse;
          this.updateStatus(TTSStatus.ERROR);
          this.onError?.(errorResponse.error_message);
        } else if (response.success) {
          // 成功响应
          const successResponse = response as TTSResponse;
          console.log('TTS success message:', successResponse.message);

          // 检查是否包含base64编码的音频数据
          if (response.audio_data) {
            console.log('Found base64 audio data in response');
            try {
              // 解码base64音频数据
              const binaryString = atob(response.audio_data);
              const bytes = new Uint8Array(binaryString.length);
              for (let i = 0; i < binaryString.length; i++) {
                bytes[i] = binaryString.charCodeAt(i);
              }
              this.audioChunks.push(bytes.buffer);
              console.log('Added base64 decoded audio chunk, size:', bytes.buffer.byteLength);
            } catch (error) {
              console.error('Failed to decode base64 audio data:', error);
            }
          }

          if (successResponse.message && (
            successResponse.message.includes('completed') ||
            successResponse.message.includes('finished') ||
            successResponse.message.includes('done') ||
            successResponse.message.includes('TTS processing completed')
          )) {
            // TTS 处理完成，等待一小段时间确保所有音频数据都收到了
            setTimeout(() => {
              console.log('Starting audio playback, chunks count:', this.audioChunks.length);
              this.playAudio();
            }, 200);
          }
        }
      } catch (error) {
        console.error('Failed to parse WebSocket message:', error);
        // 可能是纯文本消息，检查是否是完成信号
        if (typeof event.data === 'string') {
          if (event.data.includes('completed') ||
              event.data.includes('finished') ||
              event.data.includes('done')) {
            setTimeout(() => {
              console.log('Starting audio playback from text message, chunks count:', this.audioChunks.length);
              this.playAudio();
            }, 200);
          } else {
            // 可能是base64编码的音频数据
            try {
              const binaryString = atob(event.data);
              const bytes = new Uint8Array(binaryString.length);
              for (let i = 0; i < binaryString.length; i++) {
                bytes[i] = binaryString.charCodeAt(i);
              }
              this.audioChunks.push(bytes.buffer);
              console.log('Added base64 audio chunk from string, size:', bytes.buffer.byteLength);
            } catch (error) {
              // 不是base64数据，忽略
              console.log('Message is not base64 audio data:', event.data.substring(0, 100));
            }
          }
        }
      }
    }
  }

  // 播放音频
  private async playAudio() {
    console.log('playAudio called, audioContext:', !!this.audioContext, 'chunks:', this.audioChunks.length);

    if (!this.audioContext) {
      console.error('AudioContext not available');
      this.updateStatus(TTSStatus.ERROR);
      this.onError?.('音频上下文不可用');
      return;
    }

    if (this.audioChunks.length === 0) {
      console.error('No audio chunks received');
      this.updateStatus(TTSStatus.ERROR);
      this.onError?.('未接收到音频数据');
      return;
    }

    try {
      // 合并所有音频块
      const totalLength = this.audioChunks.reduce((sum, chunk) => sum + chunk.byteLength, 0);
      console.log('Total audio data length:', totalLength);

      if (totalLength === 0) {
        console.error('Total audio data length is 0');
        this.updateStatus(TTSStatus.ERROR);
        this.onError?.('音频数据为空');
        return;
      }

      const combinedBuffer = new Uint8Array(totalLength);
      let offset = 0;

      for (const chunk of this.audioChunks) {
        combinedBuffer.set(new Uint8Array(chunk), offset);
        offset += chunk.byteLength;
      }

      console.log('Combined buffer created, size:', combinedBuffer.length);

      // 确保AudioContext处于运行状态
      if (this.audioContext.state === 'suspended') {
        console.log('Resuming AudioContext');
        await this.audioContext.resume();
      }

      // 解码音频数据
      console.log('Decoding audio data...');
      this.audioBuffer = await this.audioContext.decodeAudioData(combinedBuffer.buffer.slice());

      console.log('Audio decoded successfully, duration:', this.audioBuffer.duration);

      // 创建音频源并播放
      this.audioSource = this.audioContext.createBufferSource();
      this.audioSource.buffer = this.audioBuffer;
      this.audioSource.connect(this.audioContext.destination);

      // 设置播放结束回调
      this.audioSource.onended = () => {
        console.log('Audio playback ended');
        this.updateStatus(TTSStatus.COMPLETED);
        this.onComplete?.();
        this.cleanup();
      };

      console.log('Starting audio playback...');
      this.updateStatus(TTSStatus.PLAYING);
      this.audioSource.start();

    } catch (error) {
      console.error('Failed to play audio:', error);
      this.updateStatus(TTSStatus.ERROR);

      // 提供更具体的错误信息
      if (error instanceof DOMException) {
        if (error.name === 'EncodingError') {
          this.onError?.('音频格式不支持或数据损坏');
        } else if (error.name === 'NotSupportedError') {
          this.onError?.('浏览器不支持此音频格式');
        } else {
          this.onError?.(`音频播放失败: ${error.message}`);
        }
      } else {
        this.onError?.(`音频播放失败: ${error instanceof Error ? error.message : '未知错误'}`);
      }
    }
  }

  // 发送 TTS 请求
  public async synthesize(request: TTSRequest): Promise<void> {
    try {
      // 初始化 AudioContext（在用户交互时）
      this.initAudioContext();

      this.updateStatus(TTSStatus.CONNECTING);
      this.audioChunks = []; // 清空之前的音频数据

      // 连接 WebSocket
      await this.connectWebSocket();

      // 发送 TTS 请求
      if (this.ws && this.ws.readyState === WebSocket.OPEN) {
        this.ws.send(JSON.stringify(request));
      } else {
        throw new Error('WebSocket 连接未就绪');
      }

    } catch (error) {
      console.error('TTS synthesis failed:', error);
      this.updateStatus(TTSStatus.ERROR);
      this.onError?.(error instanceof Error ? error.message : '语音合成失败');
    }
  }

  // 停止播放
  public stop() {
    if (this.audioSource) {
      this.audioSource.stop();
      this.audioSource = null;
    }
    this.cleanup();
  }

  // 清理资源
  private cleanup() {
    if (this.ws) {
      this.ws.close();
      this.ws = null;
    }
    
    if (this.audioSource) {
      this.audioSource.disconnect();
      this.audioSource = null;
    }
    
    this.audioBuffer = null;
    this.audioChunks = [];
    
    if (this.status !== TTSStatus.COMPLETED && this.status !== TTSStatus.ERROR) {
      this.updateStatus(TTSStatus.IDLE);
    }
  }

  // 销毁服务
  public destroy() {
    this.stop();
    if (this.audioContext) {
      this.audioContext.close();
      this.audioContext = null;
    }
  }
}

// 创建全局 TTS 服务实例
export const ttsService = new TTSService();
